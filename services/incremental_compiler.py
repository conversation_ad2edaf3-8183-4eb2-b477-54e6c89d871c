"""
增量编译引擎
实现智能差异分析和部分重编译
"""

import difflib
import re
import hashlib
import time
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
import asyncio
from pathlib import Path

from services.compilation_cache_service import CompilationResult, ContentDiff

@dataclass
class DocumentSection:
    """文档段落"""
    id: str
    content: str
    type: str  # heading, paragraph, code_block, list, etc.
    start_line: int
    end_line: int
    dependencies: List[str]  # 依赖的其他段落
    hash: str

@dataclass
class CompilationPatch:
    """编译补丁"""
    section_id: str
    old_vector_range: Tuple[int, int]  # 旧向量数据的范围
    new_vector_data: bytes
    typst_changes: str

class DocumentParser:
    """文档解析器"""
    
    def parse_document(self, content: str) -> List[DocumentSection]:
        """解析文档为段落"""
        sections = []
        lines = content.split('\n')
        current_section = []
        current_type = 'paragraph'
        start_line = 0
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 检测段落类型
            if line.startswith('#'):
                # 保存当前段落
                if current_section:
                    sections.append(self._create_section(
                        current_section, current_type, start_line, i-1
                    ))
                
                # 开始新的标题段落
                current_section = [line]
                current_type = 'heading'
                start_line = i
                
            elif line.startswith('```'):
                # 代码块
                if current_section:
                    sections.append(self._create_section(
                        current_section, current_type, start_line, i-1
                    ))
                
                # 找到代码块结束
                code_lines = [line]
                i += 1
                while i < len(lines) and not lines[i].startswith('```'):
                    code_lines.append(lines[i])
                    i += 1
                if i < len(lines):
                    code_lines.append(lines[i])
                
                sections.append(self._create_section(
                    code_lines, 'code_block', start_line, i
                ))
                
                current_section = []
                start_line = i + 1
                
            elif re.match(r'^[\s]*[-*+]\s', line):
                # 列表项
                if current_type != 'list':
                    if current_section:
                        sections.append(self._create_section(
                            current_section, current_type, start_line, i-1
                        ))
                    current_section = [line]
                    current_type = 'list'
                    start_line = i
                else:
                    current_section.append(line)
                    
            elif line.strip() == '':
                # 空行，可能是段落分隔
                if current_section and current_type == 'paragraph':
                    sections.append(self._create_section(
                        current_section, current_type, start_line, i-1
                    ))
                    current_section = []
                    start_line = i + 1
                elif current_section:
                    current_section.append(line)
                    
            else:
                # 普通段落
                if current_type != 'paragraph':
                    if current_section:
                        sections.append(self._create_section(
                            current_section, current_type, start_line, i-1
                        ))
                    current_section = [line]
                    current_type = 'paragraph'
                    start_line = i
                else:
                    current_section.append(line)
            
            i += 1
        
        # 处理最后一个段落
        if current_section:
            sections.append(self._create_section(
                current_section, current_type, start_line, len(lines)-1
            ))
        
        return sections
    
    def _create_section(self, lines: List[str], section_type: str, start: int, end: int) -> DocumentSection:
        """创建文档段落"""
        content = '\n'.join(lines)
        section_hash = hashlib.md5(content.encode()).hexdigest()
        section_id = f"{section_type}_{start}_{section_hash[:8]}"
        
        # 分析依赖关系
        dependencies = self._analyze_dependencies(content, section_type)
        
        return DocumentSection(
            id=section_id,
            content=content,
            type=section_type,
            start_line=start,
            end_line=end,
            dependencies=dependencies,
            hash=section_hash
        )
    
    def _analyze_dependencies(self, content: str, section_type: str) -> List[str]:
        """分析段落依赖关系"""
        dependencies = []
        
        # 检测图片引用
        image_refs = re.findall(r'!\[([^\]]*)\]\(([^)]+)\)', content)
        dependencies.extend([ref[1] for ref in image_refs])
        
        # 检测链接引用
        link_refs = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
        dependencies.extend([ref[1] for ref in link_refs])
        
        # 检测代码引用（如果有自定义语法）
        code_refs = re.findall(r'@include\(([^)]+)\)', content)
        dependencies.extend(code_refs)
        
        return dependencies

class IncrementalCompiler:
    """增量编译器"""
    
    def __init__(self):
        self.parser = DocumentParser()
        self.section_cache: Dict[str, bytes] = {}  # 段落向量缓存
        
    def analyze_diff(self, old_content: str, new_content: str) -> ContentDiff:
        """分析内容差异"""
        old_sections = self.parser.parse_document(old_content)
        new_sections = self.parser.parse_document(new_content)
        
        # 创建段落映射
        old_section_map = {s.hash: s for s in old_sections}
        new_section_map = {s.hash: s for s in new_sections}
        
        # 分析变化
        added_sections = []
        removed_sections = []
        modified_sections = []
        unchanged_count = 0
        
        # 找出新增和修改的段落
        for new_section in new_sections:
            if new_section.hash not in old_section_map:
                # 检查是否是修改的段落
                similar_section = self._find_similar_section(new_section, old_sections)
                if similar_section:
                    modified_sections.append((
                        similar_section.start_line,
                        similar_section.content,
                        new_section.content
                    ))
                else:
                    added_sections.append((new_section.start_line, new_section.content))
            else:
                unchanged_count += 1
        
        # 找出删除的段落
        for old_section in old_sections:
            if old_section.hash not in new_section_map:
                # 检查是否已经在修改列表中
                if not any(mod[1] == old_section.content for mod in modified_sections):
                    removed_sections.append((old_section.start_line, old_section.content))
        
        total_sections = max(len(old_sections), len(new_sections))
        unchanged_ratio = unchanged_count / total_sections if total_sections > 0 else 0
        
        return ContentDiff(
            added_sections=added_sections,
            removed_sections=removed_sections,
            modified_sections=modified_sections,
            unchanged_ratio=unchanged_ratio
        )
    
    def _find_similar_section(self, target_section: DocumentSection, sections: List[DocumentSection]) -> Optional[DocumentSection]:
        """找到相似的段落（用于检测修改）"""
        best_match = None
        best_ratio = 0.0
        
        for section in sections:
            if section.type == target_section.type:
                ratio = difflib.SequenceMatcher(
                    None, section.content, target_section.content
                ).ratio()
                
                if ratio > best_ratio and ratio > 0.6:  # 60% 相似度阈值
                    best_ratio = ratio
                    best_match = section
        
        return best_match
    
    async def compile_incremental(
        self, 
        new_content: str, 
        old_content: str, 
        options: Dict[str, Any]
    ) -> CompilationResult:
        """执行增量编译"""
        # 分析差异
        diff = self.analyze_diff(old_content, new_content)
        
        # 如果变化太大，执行完整编译
        if diff.unchanged_ratio < 0.3:
            return await self._full_compile(new_content, options)
        
        # 解析新文档
        new_sections = self.parser.parse_document(new_content)
        
        # 生成编译补丁
        patches = await self._generate_patches(new_sections, diff, options)
        
        # 应用补丁生成最终向量
        final_vector = await self._apply_patches(patches, new_content, options)
        
        # 转换 Typst
        from services.markdown_to_typst_service import MarkdownToTypstService
        markdown_service = MarkdownToTypstService()
        typst_content = markdown_service.convert_markdown_to_typst(new_content)
        
        return CompilationResult(
            vector_data=final_vector,
            typst_content=typst_content,
            compilation_time=0.0,  # 会在调用处计算
            cache_key="",
            dependencies=[],
            metadata={'original_content': new_content, 'incremental': True}
        )
    
    async def _generate_patches(
        self, 
        sections: List[DocumentSection], 
        diff: ContentDiff, 
        options: Dict[str, Any]
    ) -> List[CompilationPatch]:
        """生成编译补丁"""
        patches = []
        
        # 对于每个修改或新增的段落，生成补丁
        for section in sections:
            # 检查是否需要重新编译这个段落
            if self._section_needs_recompilation(section, diff):
                # 编译单个段落
                section_vector = await self._compile_section(section, options)
                
                patch = CompilationPatch(
                    section_id=section.id,
                    old_vector_range=(0, 0),  # 需要根据实际情况计算
                    new_vector_data=section_vector,
                    typst_changes=section.content
                )
                patches.append(patch)
        
        return patches
    
    def _section_needs_recompilation(self, section: DocumentSection, diff: ContentDiff) -> bool:
        """判断段落是否需要重新编译"""
        # 检查是否在修改或新增列表中
        for _, content in diff.added_sections:
            if content in section.content:
                return True
        
        for _, old_content, new_content in diff.modified_sections:
            if new_content in section.content:
                return True
        
        return False
    
    async def _compile_section(self, section: DocumentSection, options: Dict[str, Any]) -> bytes:
        """编译单个段落"""
        # 检查缓存
        if section.hash in self.section_cache:
            return self.section_cache[section.hash]
        
        # 编译段落（这里需要调用实际的编译服务）
        # 暂时返回模拟数据
        vector_data = f"compiled_section_{section.id}".encode()
        
        # 缓存结果
        self.section_cache[section.hash] = vector_data
        
        return vector_data
    
    async def _apply_patches(
        self, 
        patches: List[CompilationPatch], 
        content: str, 
        options: Dict[str, Any]
    ) -> bytes:
        """应用补丁生成最终向量"""
        # 这里需要实现向量合并逻辑
        # 暂时返回完整编译结果
        from api.typst_compile_routes import typst_compile_service
        # 使用异步编译以避免阻塞事件循环
        return await typst_compile_service.compile_markdown_to_vector_async(content, True)
    
    async def _full_compile(self, content: str, options: Dict[str, Any]) -> CompilationResult:
        """完整编译（回退方案）"""
        from api.typst_compile_routes import typst_compile_service
        from services.markdown_to_typst_service import MarkdownToTypstService
        
        start_time = time.time()
        
        markdown_service = MarkdownToTypstService()
        typst_content = markdown_service.convert_markdown_to_typst(content)
        # 使用异步编译避免阻塞
        vector_data = await typst_compile_service.compile_markdown_to_vector_async(content, True)
        
        compilation_time = time.time() - start_time
        
        return CompilationResult(
            vector_data=vector_data,
            typst_content=typst_content,
            compilation_time=compilation_time,
            cache_key="",
            dependencies=[],
            metadata={'original_content': content, 'incremental': False}
        )
