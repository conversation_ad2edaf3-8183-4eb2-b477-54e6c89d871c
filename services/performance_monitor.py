"""
性能监控和分析服务
提供编译性能指标、瓶颈分析、优化建议
"""

import time
import psutil
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics
import logging

logger = logging.getLogger(__name__)

@dataclass
class CompilationMetrics:
    """编译指标"""
    session_id: str
    user_id: str
    content_length: int
    compilation_time: float
    cache_hit: bool
    incremental: bool
    memory_usage: float
    cpu_usage: float
    timestamp: float
    error: Optional[str] = None

@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    active_connections: int
    compilation_queue_size: int
    cache_hit_rate: float
    timestamp: float

@dataclass
class PerformanceReport:
    """性能报告"""
    period_start: float
    period_end: float
    total_compilations: int
    successful_compilations: int
    failed_compilations: int
    average_compilation_time: float
    median_compilation_time: float
    p95_compilation_time: float
    cache_hit_rate: float
    incremental_rate: float
    peak_memory_usage: float
    peak_cpu_usage: float
    bottlenecks: List[str]
    recommendations: List[str]

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_metrics_history: int = 10000):
        self.compilation_metrics: deque = deque(maxlen=max_metrics_history)
        self.system_metrics: deque = deque(maxlen=1000)  # 系统指标保留更少
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.compilation_queue_size = 0
        
        # 性能阈值
        self.thresholds = {
            'compilation_time_warning': 2.0,  # 2秒
            'compilation_time_critical': 5.0,  # 5秒
            'memory_usage_warning': 80.0,  # 80%
            'memory_usage_critical': 90.0,  # 90%
            'cpu_usage_warning': 80.0,  # 80%
            'cpu_usage_critical': 90.0,  # 90%
            'cache_hit_rate_warning': 0.6,  # 60%
        }
        
        # 启动系统监控
        asyncio.create_task(self._system_monitor_loop())
    
    def record_compilation(self, metrics: CompilationMetrics):
        """记录编译指标"""
        self.compilation_metrics.append(metrics)
        
        # 检查性能警告
        self._check_performance_warnings(metrics)
        
        # 更新会话信息
        self._update_session_info(metrics)
    
    def record_system_metrics(self):
        """记录系统指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            cache_hit_rate = self._calculate_cache_hit_rate()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_usage=disk.percent,
                active_connections=len(self.active_sessions),
                compilation_queue_size=self.compilation_queue_size,
                cache_hit_rate=cache_hit_rate,
                timestamp=time.time()
            )
            
            self.system_metrics.append(metrics)
            
        except Exception as e:
            logger.error(f"记录系统指标失败: {e}")
    
    def generate_performance_report(self, period_hours: float = 1.0) -> PerformanceReport:
        """生成性能报告"""
        current_time = time.time()
        period_start = current_time - (period_hours * 3600)
        
        # 筛选时间范围内的指标
        period_metrics = [
            m for m in self.compilation_metrics 
            if m.timestamp >= period_start
        ]
        
        if not period_metrics:
            return self._empty_report(period_start, current_time)
        
        # 计算基础统计
        total_compilations = len(period_metrics)
        successful_compilations = len([m for m in period_metrics if m.error is None])
        failed_compilations = total_compilations - successful_compilations
        
        compilation_times = [m.compilation_time for m in period_metrics if m.error is None]
        
        if compilation_times:
            average_time = statistics.mean(compilation_times)
            median_time = statistics.median(compilation_times)
            p95_time = self._percentile(compilation_times, 95)
        else:
            average_time = median_time = p95_time = 0.0
        
        # 缓存命中率
        cache_hits = len([m for m in period_metrics if m.cache_hit])
        cache_hit_rate = cache_hits / total_compilations if total_compilations > 0 else 0.0
        
        # 增量编译率
        incremental_compilations = len([m for m in period_metrics if m.incremental])
        incremental_rate = incremental_compilations / total_compilations if total_compilations > 0 else 0.0
        
        # 系统资源峰值
        period_system_metrics = [
            m for m in self.system_metrics 
            if m.timestamp >= period_start
        ]
        
        if period_system_metrics:
            peak_memory = max(m.memory_percent for m in period_system_metrics)
            peak_cpu = max(m.cpu_percent for m in period_system_metrics)
        else:
            peak_memory = peak_cpu = 0.0
        
        # 分析瓶颈和建议
        bottlenecks = self._analyze_bottlenecks(period_metrics, period_system_metrics)
        recommendations = self._generate_recommendations(period_metrics, period_system_metrics)
        
        return PerformanceReport(
            period_start=period_start,
            period_end=current_time,
            total_compilations=total_compilations,
            successful_compilations=successful_compilations,
            failed_compilations=failed_compilations,
            average_compilation_time=average_time,
            median_compilation_time=median_time,
            p95_compilation_time=p95_time,
            cache_hit_rate=cache_hit_rate,
            incremental_rate=incremental_rate,
            peak_memory_usage=peak_memory,
            peak_cpu_usage=peak_cpu,
            bottlenecks=bottlenecks,
            recommendations=recommendations
        )
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """获取实时指标"""
        recent_compilations = list(self.compilation_metrics)[-100:]  # 最近100次编译
        recent_system = list(self.system_metrics)[-10:]  # 最近10次系统指标
        
        if recent_compilations:
            recent_times = [m.compilation_time for m in recent_compilations if m.error is None]
            avg_time = statistics.mean(recent_times) if recent_times else 0.0
            
            recent_cache_hits = len([m for m in recent_compilations if m.cache_hit])
            cache_hit_rate = recent_cache_hits / len(recent_compilations)
        else:
            avg_time = 0.0
            cache_hit_rate = 0.0
        
        current_system = recent_system[-1] if recent_system else None
        
        return {
            'active_sessions': len(self.active_sessions),
            'compilation_queue_size': self.compilation_queue_size,
            'recent_average_time': avg_time,
            'recent_cache_hit_rate': cache_hit_rate,
            'current_cpu_usage': current_system.cpu_percent if current_system else 0.0,
            'current_memory_usage': current_system.memory_percent if current_system else 0.0,
            'timestamp': time.time()
        }
    
    def _check_performance_warnings(self, metrics: CompilationMetrics):
        """检查性能警告"""
        warnings = []
        
        if metrics.compilation_time > self.thresholds['compilation_time_critical']:
            warnings.append(f"编译时间过长: {metrics.compilation_time:.2f}s")
        elif metrics.compilation_time > self.thresholds['compilation_time_warning']:
            warnings.append(f"编译时间较长: {metrics.compilation_time:.2f}s")
        
        if metrics.memory_usage > self.thresholds['memory_usage_critical']:
            warnings.append(f"内存使用率过高: {metrics.memory_usage:.1f}%")
        elif metrics.memory_usage > self.thresholds['memory_usage_warning']:
            warnings.append(f"内存使用率较高: {metrics.memory_usage:.1f}%")
        
        if warnings:
            logger.warning(f"性能警告 [session={metrics.session_id}]: {'; '.join(warnings)}")
    
    def _update_session_info(self, metrics: CompilationMetrics):
        """更新会话信息"""
        session_id = metrics.session_id
        
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = {
                'user_id': metrics.user_id,
                'start_time': metrics.timestamp,
                'compilation_count': 0,
                'total_time': 0.0,
                'cache_hits': 0,
                'incremental_count': 0
            }
        
        session = self.active_sessions[session_id]
        session['compilation_count'] += 1
        session['total_time'] += metrics.compilation_time
        session['last_activity'] = metrics.timestamp
        
        if metrics.cache_hit:
            session['cache_hits'] += 1
        if metrics.incremental:
            session['incremental_count'] += 1
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        if not self.compilation_metrics:
            return 0.0
        
        recent_metrics = list(self.compilation_metrics)[-1000:]  # 最近1000次
        cache_hits = len([m for m in recent_metrics if m.cache_hit])
        return cache_hits / len(recent_metrics)
    
    def _analyze_bottlenecks(self, compilation_metrics: List[CompilationMetrics], system_metrics: List[SystemMetrics]) -> List[str]:
        """分析性能瓶颈"""
        bottlenecks = []
        
        if compilation_metrics:
            # 编译时间分析
            slow_compilations = [m for m in compilation_metrics if m.compilation_time > self.thresholds['compilation_time_warning']]
            if len(slow_compilations) / len(compilation_metrics) > 0.2:  # 超过20%的编译较慢
                bottlenecks.append("编译速度瓶颈：大量编译耗时过长")
            
            # 缓存命中率分析
            cache_hits = len([m for m in compilation_metrics if m.cache_hit])
            cache_hit_rate = cache_hits / len(compilation_metrics)
            if cache_hit_rate < self.thresholds['cache_hit_rate_warning']:
                bottlenecks.append(f"缓存效率低：命中率仅 {cache_hit_rate:.1%}")
            
            # 增量编译分析
            incremental_count = len([m for m in compilation_metrics if m.incremental])
            incremental_rate = incremental_count / len(compilation_metrics)
            if incremental_rate < 0.3:  # 增量编译率低于30%
                bottlenecks.append(f"增量编译使用率低：仅 {incremental_rate:.1%}")
        
        if system_metrics:
            # 系统资源分析
            high_cpu_count = len([m for m in system_metrics if m.cpu_percent > self.thresholds['cpu_usage_warning']])
            if high_cpu_count / len(system_metrics) > 0.3:
                bottlenecks.append("CPU 使用率持续偏高")
            
            high_memory_count = len([m for m in system_metrics if m.memory_percent > self.thresholds['memory_usage_warning']])
            if high_memory_count / len(system_metrics) > 0.3:
                bottlenecks.append("内存使用率持续偏高")
        
        return bottlenecks
    
    def _generate_recommendations(self, compilation_metrics: List[CompilationMetrics], system_metrics: List[SystemMetrics]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if compilation_metrics:
            # 基于编译指标的建议
            cache_hit_rate = len([m for m in compilation_metrics if m.cache_hit]) / len(compilation_metrics)
            if cache_hit_rate < 0.6:
                recommendations.append("建议优化缓存策略，提高缓存命中率")
            
            incremental_rate = len([m for m in compilation_metrics if m.incremental]) / len(compilation_metrics)
            if incremental_rate < 0.4:
                recommendations.append("建议优化增量编译算法，提高增量编译使用率")
            
            avg_time = statistics.mean([m.compilation_time for m in compilation_metrics if m.error is None])
            if avg_time > 2.0:
                recommendations.append("建议优化编译流程，减少编译时间")
        
        if system_metrics:
            # 基于系统指标的建议
            avg_cpu = statistics.mean([m.cpu_percent for m in system_metrics])
            if avg_cpu > 70:
                recommendations.append("建议增加 CPU 资源或优化编译算法")
            
            avg_memory = statistics.mean([m.memory_percent for m in system_metrics])
            if avg_memory > 80:
                recommendations.append("建议增加内存资源或优化内存使用")
        
        return recommendations
    
    def _empty_report(self, period_start: float, period_end: float) -> PerformanceReport:
        """空报告"""
        return PerformanceReport(
            period_start=period_start,
            period_end=period_end,
            total_compilations=0,
            successful_compilations=0,
            failed_compilations=0,
            average_compilation_time=0.0,
            median_compilation_time=0.0,
            p95_compilation_time=0.0,
            cache_hit_rate=0.0,
            incremental_rate=0.0,
            peak_memory_usage=0.0,
            peak_cpu_usage=0.0,
            bottlenecks=[],
            recommendations=[]
        )
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    async def _system_monitor_loop(self):
        """系统监控循环"""
        while True:
            try:
                self.record_system_metrics()
                await asyncio.sleep(10)  # 每10秒记录一次
            except Exception as e:
                logger.error(f"系统监控循环错误: {e}")
                await asyncio.sleep(30)  # 出错时等待30秒

# 全局性能监控实例
performance_monitor = PerformanceMonitor()
