"""
智能编译缓存服务
实现多层缓存策略，支持增量编译和智能失效
"""

import hashlib
import json
import time
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import redis
import pickle
from abc import ABC, abstractmethod

@dataclass
class CompilationResult:
    """编译结果数据结构"""
    vector_data: bytes
    typst_content: str
    compilation_time: float
    cache_key: str
    dependencies: List[str]  # 依赖的资源列表
    metadata: Dict[str, Any]

@dataclass
class ContentDiff:
    """内容差异数据结构"""
    added_sections: List[Tuple[int, str]]
    removed_sections: List[Tuple[int, str]]
    modified_sections: List[Tuple[int, str, str]]
    unchanged_ratio: float

class CacheStrategy(ABC):
    """缓存策略抽象基类"""
    
    @abstractmethod
    def get_cache_key(self, content: str, options: Dict[str, Any]) -> str:
        pass
    
    @abstractmethod
    def should_invalidate(self, old_content: str, new_content: str) -> bool:
        pass

class ContentHashStrategy(CacheStrategy):
    """基于内容哈希的缓存策略"""
    
    def get_cache_key(self, content: str, options: Dict[str, Any]) -> str:
        # 创建包含内容和选项的复合哈希
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        options_hash = hashlib.sha256(json.dumps(options, sort_keys=True).encode()).hexdigest()
        return f"compile:{content_hash[:16]}:{options_hash[:8]}"
    
    def should_invalidate(self, old_content: str, new_content: str) -> bool:
        return old_content != new_content

class SemanticDiffStrategy(CacheStrategy):
    """基于语义差异的缓存策略"""
    
    def get_cache_key(self, content: str, options: Dict[str, Any]) -> str:
        # 提取语义结构而非原始内容
        semantic_structure = self._extract_semantic_structure(content)
        structure_hash = hashlib.sha256(json.dumps(semantic_structure).encode()).hexdigest()
        options_hash = hashlib.sha256(json.dumps(options, sort_keys=True).encode()).hexdigest()
        return f"semantic:{structure_hash[:16]}:{options_hash[:8]}"
    
    def _extract_semantic_structure(self, content: str) -> Dict[str, Any]:
        """提取 Markdown 的语义结构"""
        import re
        
        structure = {
            'headings': re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE),
            'code_blocks': len(re.findall(r'```[\s\S]*?```', content)),
            'lists': len(re.findall(r'^[\s]*[-*+]\s', content, re.MULTILINE)),
            'links': len(re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)),
            'images': len(re.findall(r'!\[([^\]]*)\]\(([^)]+)\)', content)),
            'tables': len(re.findall(r'\|.*\|', content)),
            'word_count': len(content.split()),
        }
        return structure
    
    def should_invalidate(self, old_content: str, new_content: str) -> bool:
        old_structure = self._extract_semantic_structure(old_content)
        new_structure = self._extract_semantic_structure(new_content)
        return old_structure != new_structure

class CompilationCacheService:
    """编译缓存服务"""
    
    def __init__(self, redis_client: redis.Redis, strategy: CacheStrategy):
        self.redis = redis_client
        self.strategy = strategy
        self.cache_ttl = 3600 * 24  # 24小时
        self.max_cache_size = 1000  # 最大缓存条目数
        
    async def get_cached_result(
        self, 
        content: str, 
        options: Dict[str, Any] = None
    ) -> Optional[CompilationResult]:
        """获取缓存的编译结果"""
        if options is None:
            options = {}
            
        cache_key = self.strategy.get_cache_key(content, options)
        
        try:
            cached_data = self.redis.get(cache_key)
            if cached_data:
                result = pickle.loads(cached_data)
                # 更新访问时间
                self.redis.expire(cache_key, self.cache_ttl)
                return result
        except Exception as e:
            print(f"缓存读取失败: {e}")
            
        return None
    
    async def cache_result(
        self, 
        content: str, 
        result: CompilationResult,
        options: Dict[str, Any] = None
    ) -> None:
        """缓存编译结果"""
        if options is None:
            options = {}
            
        cache_key = self.strategy.get_cache_key(content, options)
        result.cache_key = cache_key
        
        try:
            # 序列化结果
            serialized_result = pickle.dumps(result)
            
            # 检查缓存大小限制
            await self._enforce_cache_limits()
            
            # 存储到 Redis
            self.redis.setex(cache_key, self.cache_ttl, serialized_result)
            
            # 更新缓存统计
            self._update_cache_stats(cache_key, len(serialized_result))
            
        except Exception as e:
            print(f"缓存写入失败: {e}")
    
    async def invalidate_related_cache(self, content: str) -> None:
        """智能失效相关缓存"""
        # 获取所有缓存键（使用 SCAN 避免阻塞）
        pattern = "compile:*" if isinstance(self.strategy, ContentHashStrategy) else "semantic:*"
        invalidated_count = 0
        try:
            for key in self.redis.scan_iter(match=pattern, count=500):
                try:
                    cached_data = self.redis.get(key)
                    if cached_data:
                        cached_result = pickle.loads(cached_data)
                        if self.strategy.should_invalidate(cached_result.metadata.get('original_content', ''), content):
                            self.redis.delete(key)
                            invalidated_count += 1
                except Exception:
                    continue
        except Exception as e:
            print(f"扫描缓存键失败: {e}")
        
        print(f"智能失效了 {invalidated_count} 个相关缓存")
    
    async def _enforce_cache_limits(self) -> None:
        """强制执行缓存大小限制"""
        # 统计数量（使用 SCAN 计数以避免阻塞）
        def _count_keys(pattern: str) -> int:
            count = 0
            try:
                for _ in self.redis.scan_iter(match=pattern, count=1000):
                    count += 1
            except Exception:
                pass
            return count

        cache_count = _count_keys("compile:*") + _count_keys("semantic:*")
        
        if cache_count >= self.max_cache_size:
            # 使用近似 LRU：依据 TTL 推导最近访问，删除最旧的 10%
            all_keys = []
            for key in self.redis.scan_iter(match="compile:*", count=1000):
                all_keys.append(key)
            for key in self.redis.scan_iter(match="semantic:*", count=1000):
                all_keys.append(key)
            
            key_access_times = []
            for key in all_keys:
                try:
                    ttl = self.redis.ttl(key)
                    if ttl and ttl > 0:
                        last_access = self.cache_ttl - ttl
                        key_access_times.append((key, last_access))
                except Exception:
                    continue
            
            key_access_times.sort(key=lambda x: x[1])
            delete_count = max(1, int(len(key_access_times) * 0.1)) if key_access_times else 0
            for key, _ in key_access_times[:delete_count]:
                try:
                    self.redis.delete(key)
                except Exception:
                    continue
    
    def _update_cache_stats(self, cache_key: str, size: int) -> None:
        """更新缓存统计信息"""
        stats_key = "cache_stats"
        current_time = int(time.time())
        
        stats = {
            'total_entries': self.redis.dbsize(),
            'last_update': current_time,
            'total_size_bytes': size,
        }
        
        self.redis.hset(stats_key, mapping=stats)
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats_key = "cache_stats"
        stats = self.redis.hgetall(stats_key)
        # 兼容 decode_responses=False 的字节键
        def _get_int(d, k, default=0):
            key = k if isinstance(next(iter(d.keys()), b''), str) else k.encode()
            try:
                return int(d.get(key, default))
            except Exception:
                return default
        
        # 使用 INFO memory 统计已用内存，避免 memory_usage 需要 key 的问题
        mem_used = 0
        try:
            info = self.redis.info(section='memory')
            mem_used = int(info.get('used_memory', 0))
        except Exception:
            mem_used = 0
        
        return {
            'total_entries': _get_int(stats, 'total_entries', 0),
            'last_update': _get_int(stats, 'last_update', 0),
            'total_size_bytes': _get_int(stats, 'total_size_bytes', 0),
            'hit_rate': self._calculate_hit_rate(),
            'memory_usage': mem_used,
        }
    
    def _calculate_hit_rate(self) -> float:
        """计算缓存命中率"""
        # 这里可以实现更复杂的命中率统计
        # 暂时返回模拟值
        return 0.85

# 工厂函数
def create_cache_service(redis_url: str = None, strategy_type: str = "content_hash") -> CompilationCacheService:
    """创建缓存服务实例"""
    import os

    # 从环境变量获取 Redis 配置
    if redis_url is None:
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    try:
        redis_client = redis.from_url(redis_url, decode_responses=False)
        # 测试连接
        redis_client.ping()
    except Exception as e:
        print(f"Redis 连接失败: {e}")
        print("使用内存缓存作为后备方案")
        # 使用简单的内存字典作为后备
        redis_client = MemoryCache()

    if strategy_type == "semantic":
        strategy = SemanticDiffStrategy()
    else:
        strategy = ContentHashStrategy()

    return CompilationCacheService(redis_client, strategy)

class MemoryCache:
    """内存缓存后备方案"""

    def __init__(self):
        self._cache = {}
        self._ttl = {}

    def get(self, key):
        import time
        if key in self._cache:
            if key in self._ttl and time.time() > self._ttl[key]:
                del self._cache[key]
                del self._ttl[key]
                return None
            return self._cache[key]
        return None

    def setex(self, key, ttl, value):
        import time
        self._cache[key] = value
        self._ttl[key] = time.time() + ttl

    def delete(self, key):
        if key in self._cache:
            del self._cache[key]
        if key in self._ttl:
            del self._ttl[key]

    def keys(self, pattern):
        # 简单的模式匹配
        import fnmatch
        return [k for k in self._cache.keys() if fnmatch.fnmatch(k, pattern.replace('*', '*'))]

    def expire(self, key, ttl):
        import time
        if key in self._cache:
            self._ttl[key] = time.time() + ttl

    def ttl(self, key):
        import time
        if key in self._ttl:
            remaining = self._ttl[key] - time.time()
            return max(0, remaining)
        return -1

    def hset(self, key, mapping):
        self._cache[key] = mapping

    def hgetall(self, key):
        return self._cache.get(key, {})

    def dbsize(self):
        return len(self._cache)

    def memory_usage(self):
        return len(str(self._cache))
