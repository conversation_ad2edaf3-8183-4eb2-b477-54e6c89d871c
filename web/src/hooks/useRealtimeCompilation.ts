/**
 * 实时编译 Hook
 * 提供 WebSocket 连接、防抖、缓存等功能
 */

import { useState, useEffect, useCallback, useRef } from 'react'
// 使用本文件内的 useDebounce 实现，无需外部模块

interface CompilationResult {
  vectorData: string // base64 encoded
  typstContent: string
  compilationTime: number
  fromCache: boolean
  cacheKey: string
}

interface CompilationProgress {
  stage: 'diff_analysis' | 'incremental_compile' | 'markdown_to_typst' | 'typst_to_vector' | 'vector_generation'
  progress: number
  diffInfo?: {
    unchangedRatio: number
    modifiedSections: number
  }
}

interface CompilationStats {
  totalCompilations: number
  cacheHits: number
  averageTime: number
  incrementalRatio: number
}

interface UseRealtimeCompilationOptions {
  debounceMs?: number
  autoCompile?: boolean
  enableCache?: boolean
  enableIncremental?: boolean
}

interface UseRealtimeCompilationReturn {
  // 状态
  isConnected: boolean
  isCompiling: boolean
  result: CompilationResult | null
  progress: CompilationProgress | null
  error: string | null
  stats: CompilationStats | null
  
  // 方法
  compile: (content: string, force?: boolean) => void
  connect: () => void
  disconnect: () => void
  clearCache: () => void
  
  // 配置
  setAutoCompile: (enabled: boolean) => void
  setEnableCache: (enabled: boolean) => void
  setEnableIncremental: (enabled: boolean) => void
}

export function useRealtimeCompilation(
  options: UseRealtimeCompilationOptions = {}
): UseRealtimeCompilationReturn {
  const {
    debounceMs = 300,
    autoCompile = true,
    enableCache = true,
    enableIncremental = true
  } = options

  // 状态
  const [isConnected, setIsConnected] = useState(false)
  const [isCompiling, setIsCompiling] = useState(false)
  const [result, setResult] = useState<CompilationResult | null>(null)
  const [progress, setProgress] = useState<CompilationProgress | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<CompilationStats | null>(null)
  
  // 配置状态
  const [autoCompileEnabled, setAutoCompileEnabled] = useState(autoCompile)
  const [cacheEnabled, setCacheEnabled] = useState(enableCache)
  const [incrementalEnabled, setIncrementalEnabled] = useState(enableIncremental)
  
  // 引用
  const wsRef = useRef<WebSocket | null>(null)
  const sessionIdRef = useRef<string>(generateSessionId())
  const compilationCountRef = useRef(0)
  const lastContentRef = useRef<string>('')
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()
  
  // 防抖内容
  const [content, setContent] = useState('')
  const debouncedContent = useDebounce(content, debounceMs)
  
  // 更新统计信息（先于消息处理声明，避免使用前声明）
  const updateStats = useCallback((result: CompilationResult) => {
    setStats(prevStats => {
      const newStats = prevStats || {
        totalCompilations: 0,
        cacheHits: 0,
        averageTime: 0,
        incrementalRatio: 0
      }
      
      const totalCompilations = newStats.totalCompilations + 1
      const cacheHits = newStats.cacheHits + (result.fromCache ? 1 : 0)
      const averageTime = (newStats.averageTime * newStats.totalCompilations + result.compilationTime) / totalCompilations
      
      return {
        totalCompilations,
        cacheHits,
        averageTime,
        incrementalRatio: newStats.incrementalRatio // 需要从后端获取
      }
    })
  }, [])

  // 处理 WebSocket 消息（先于 connect 声明，避免使用前声明）
  interface WsMessage { type: string; [key: string]: unknown }
  const handleWebSocketMessage = useCallback((message: WsMessage) => {
    switch (message.type) {
      case 'compilation_started':
        setIsCompiling(true)
        setProgress(null)
        setError(null)
        break
        
      case 'compilation_progress':
        {
          const di = (message as { diff_info?: { unchanged_ratio?: number; modified_sections?: number } }).diff_info
          const stage = (message as { stage?: string }).stage
          const progressVal = (message as { progress?: number }).progress
          setProgress({
            stage: (typeof stage === 'string' ? stage : 'markdown_to_typst') as CompilationProgress['stage'],
            progress: Number(progressVal ?? 0),
            diffInfo: di
              ? {
                  unchangedRatio: Number(di.unchanged_ratio ?? 0),
                  modifiedSections: Number(di.modified_sections ?? 0),
                }
              : undefined,
          })
        }
        break
        
      case 'compilation_complete':
        setIsCompiling(false)
        setProgress(null)
        {
          // 通过 unknown 中转，避免直接断言 WsMessage 导致的 2352 报错
          const r = (message as unknown as { result: { vector_data: string; typst_content: string; compilation_time: number; from_cache: boolean; cache_key: string } }).result
          const mapped: CompilationResult = {
            vectorData: r.vector_data,
            typstContent: r.typst_content,
            compilationTime: r.compilation_time,
            fromCache: r.from_cache,
            cacheKey: r.cache_key,
          }
          setResult(mapped)
          compilationCountRef.current++
          updateStats(mapped)
        }
        break
        
      case 'compilation_error':
        setIsCompiling(false)
        setProgress(null)
        setError(typeof (message as { error?: unknown }).error === 'string' ? (message as { error?: string }).error! : '编译错误')
        break
        
      case 'cache_stats':
        // 处理缓存统计信息
        console.log('缓存统计:', (message as { stats?: unknown }).stats)
        break
        
      case 'pong':
        // 心跳响应
        break
        
      default:
        console.log('未知消息类型:', (message as { type?: unknown }).type)
    }
  }, [updateStats])

  // WebSocket 连接（在消息处理之后声明）
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }
    
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/ws/compile/${sessionIdRef.current}`
    
    try {
      const ws = new WebSocket(wsUrl)
      
      ws.onopen = () => {
        console.log('WebSocket 连接已建立')
        setIsConnected(true)
        setError(null)
        
        // 清除重连定时器
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current)
        }
      }
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleWebSocketMessage(message)
        } catch (err) {
          console.error('解析 WebSocket 消息失败:', err)
        }
      }
      
      ws.onclose = (event) => {
        console.log('WebSocket 连接已关闭:', event.code, event.reason)
        setIsConnected(false)
        setIsCompiling(false)
        
        // 自动重连（除非是正常关闭）
        if (event.code !== 1000) {
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('尝试重新连接...')
            connect()
          }, 3000)
        }
      }
      
      ws.onerror = (error) => {
        console.error('WebSocket 错误:', error)
        setError('连接错误，请检查网络')
      }
      
      wsRef.current = ws
      
    } catch (err) {
      console.error('创建 WebSocket 连接失败:', err)
      setError('无法建立连接')
    }
  }, [handleWebSocketMessage])
  
  // 断开连接
  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected')
      wsRef.current = null
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    setIsConnected(false)
    setIsCompiling(false)
  }, [])
  
  
  
  // 编译函数
  const compile = useCallback((content: string, force = false) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      setError('WebSocket 未连接')
      return
    }
    
    // 避免重复编译相同内容
    if (!force && content === lastContentRef.current && result) {
      return
    }
    
    lastContentRef.current = content
    
    const message = {
      type: 'compile',
      content,
      options: {
        enable_cache: cacheEnabled,
        enable_incremental: incrementalEnabled
      },
      force_recompile: force
    }
    
    wsRef.current.send(JSON.stringify(message))
  }, [cacheEnabled, incrementalEnabled, result])
  
  // 清除缓存
  const clearCache = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type: 'clear_cache' }))
    }
  }, [])
  
  // 自动编译
  useEffect(() => {
    if (autoCompileEnabled && debouncedContent && isConnected) {
      compile(debouncedContent)
    }
  }, [debouncedContent, autoCompileEnabled, isConnected, compile])
  
  // 组件挂载时连接
  useEffect(() => {
    connect()
    
    // 心跳检测
    const heartbeat = setInterval(() => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // 30秒心跳
    
    return () => {
      clearInterval(heartbeat)
      disconnect()
    }
  }, [connect, disconnect])
  
  // 公开内容设置方法（用于手动触发编译）
  const setContentForCompilation = useCallback((newContent: string) => {
    setContent(newContent)
  }, [])
  
  return {
    // 状态
    isConnected,
    isCompiling,
    result,
    progress,
    error,
    stats,
    
    // 方法
    compile: useCallback((content: string, force?: boolean) => {
      setContentForCompilation(content)
      if (force) {
        compile(content, true)
      }
    }, [setContentForCompilation, compile]),
    connect,
    disconnect,
    clearCache,
    
    // 配置
    setAutoCompile: setAutoCompileEnabled,
    setEnableCache: setCacheEnabled,
    setEnableIncremental: setIncrementalEnabled
  }
}

// 辅助函数
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 防抖 Hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}
