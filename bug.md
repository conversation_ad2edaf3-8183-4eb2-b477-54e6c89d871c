# 结论先行

建议优先落地 **快速修复清单（P0 级）** —— 修复 dev 环境 `backendUrl` 覆盖、刷新令牌协议不一致、补齐文档详情/删除/导出接口。随后在 **两周迭代窗口内完成 API 前缀统一、服务合并与存储规范化**，最终交付一个标准化、可维护、易于前后端协作的 V1 API 面。

---

## 后端问题与改进

### 主要痛点

* **路由前缀不统一**：`/api` 与 `/api/v1` 混用，`main.py` 多次重复 include。
* **接口缺失**：前端期望的 `/documents/{id}`、删除与导出端点后端未实现。
* **刷新令牌协议不一致**：前端用 Bearer header，后端只接受 query/body。
* **静态挂载异常**：`APIRouter.mount` 不生效，应由 `app.mount` 管理。
* **存储路径混乱**：`../tmp` 与 `PROJECT_ROOT/tmp` 并存，存在跨平台风险。
* **服务职责模糊**：SSE 与 AIChatService 重叠；Typst 有两套编译路由。

### 改进方案

1. **统一前缀**：所有路由挂载到 `/api/v1` 下；保留 `/api/...` 30 天别名 + Deprecation Warning。
2. **补齐接口**：新增文档详情、删除、导出；与前端代理一致。
3. **刷新令牌双兼容**：同时支持 Bearer header + JSON body，逐步收敛。
4. **静态/存储规范化**：统一 `BASE_DIR/tmp`；静态结果用 `app.mount` 或改 `FileResponse`。
5. **服务层解耦**：SSE → `AIStreamService`；Typst 路由合并；DB 仅保留 SQLAlchemy。
6. **Schema 一致性**：JSON 字段 → `List[Dict]`，全局输出统一 `{success, data, message, error_code}`。

---

## 前端问题与改进

### 主要痛点

* **环境覆盖错误**：`ai-config.ts` 在 dev 把 `backendUrl` 误写成 3000。
* **代理与后端不一致**：PDF、Markdown-to-Typst 未代理；文档导出代理有但后端缺失。
* **认证流割裂**：中间件依赖 Cookie，`api-client.ts` 存在 localStorage。

### 改进方案

1. **修正 dev 配置**：`backendUrl` 默认应为 8000（或由 env 决定）。
2. **认证流统一**：access/refresh token 放入 Cookie；401 自动刷新并重放。
3. **代理矩阵收敛**：仅代理需 SSE/CORS 的路由，其余直连后端；保持覆盖一致。
4. **客户端库对齐**：`api-client.ts` 错误处理统一化，直连/代理逻辑与 Next 中间件同步。

---

## 实施路径

### 快速修复（当天完成）

* 移除 `markdown_to_typst_router` 重复 include。
* 修复 PDF 静态挂载。
* 新增 `/documents/{id}`、删除、导出 stub 接口。
* 刷新令牌端点支持 Bearer + Body。
* 修复前端 dev `backendUrl`。

### 演进重构（两周内）

* 统一 API 前缀与路由分组。
* 新增 `/api/v1/health` 聚合检查。
* Typst 服务整合，SSE 复用 AIChatService。
* 存储目录与静态路径标准化。
* DB 单栈化（SQLAlchemy）。
* OpenAPI 文档整理、测试覆盖补齐。

---

## 标准化 V1 API 面（草案）

* **Auth**：`/api/v1/auth/login`、`/api/v1/auth/refresh`
* **Prose**：`/api/v1/prose/generate`（SSE）
* **Documents**：上传、列表、详情、删除、导出
* **Typst**：`/api/v1/typst-compile/*`
* **PDF**：上传与结果访问

---

## 验证与回退

* **验证**：端到端脚本（登录→上传→详情→删除→编译→SSE）+ 手工路径回归。
* **回退**：旧 `/api/...` 别名保留 30 天；新端点可先返回 stub；关键改动 behind feature flag。


