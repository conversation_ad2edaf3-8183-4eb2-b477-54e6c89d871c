"""
WebSocket 实时编译路由 - 简化版本
支持基本的实时编译和进度推送
"""

import json
import time
from typing import Dict
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class ConnectionManager:
    """WebSocket 连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        
    async def connect(self, websocket: WebSocket, session_id: str):
        """建立连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logger.info(f"WebSocket 连接建立: session={session_id}")
    
    def disconnect(self, session_id: str):
        """断开连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        logger.info(f"WebSocket 连接断开: session={session_id}")
    
    async def send_personal_message(self, message: dict, session_id: str):
        """发送个人消息"""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")

# 全局连接管理器
manager = ConnectionManager()

@router.websocket("/ws/compile/{session_id}")
async def websocket_compile_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket 编译端点 - 简化版本"""
    await manager.connect(websocket, session_id)
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get('type')
            
            if message_type == 'compile':
                # 处理编译请求
                await handle_compile_request(message, session_id)
                
            elif message_type == 'ping':
                await manager.send_personal_message({
                    'type': 'pong',
                    'timestamp': time.time()
                }, session_id)
                
    except WebSocketDisconnect:
        manager.disconnect(session_id)
    except Exception as e:
        logger.error(f"WebSocket 错误: {e}", exc_info=True)
        manager.disconnect(session_id)

async def handle_compile_request(message: dict, session_id: str):
    """处理编译请求"""
    content = message.get('content', '')
    
    if not content:
        await manager.send_personal_message({
            'type': 'compilation_error',
            'error': '没有内容可以编译'
        }, session_id)
        return
    
    # 发送编译开始消息
    await manager.send_personal_message({
        'type': 'compilation_started',
        'timestamp': time.time()
    }, session_id)
    
    try:
        # 调用现有的编译服务
        from api.typst_compile_routes import typst_compile_service
        vector_data = typst_compile_service.compile_markdown_to_vector(content, True)
        
        # 将向量数据转换为 base64
        import base64
        vector_b64 = base64.b64encode(vector_data).decode('utf-8')
        
        # 发送编译完成消息
        await manager.send_personal_message({
            'type': 'compilation_complete',
            'result': {
                'vector_data': vector_b64,
                'compilation_time': 0.0,  # 简化版本不计算时间
                'from_cache': False
            },
            'timestamp': time.time()
        }, session_id)
        
    except Exception as e:
        logger.error(f"编译失败: {e}", exc_info=True)
        await manager.send_personal_message({
            'type': 'compilation_error',
            'error': str(e),
            'timestamp': time.time()
        }, session_id)
